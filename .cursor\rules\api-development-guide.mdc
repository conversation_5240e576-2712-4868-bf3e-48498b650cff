---
description: 
globs: 
alwaysApply: false
---
# API 开发指南

## 后端模拟 API 开发

### 项目结构
后端模拟服务位于 [apps/backend-mock](mdc:apps/backend-mock)，使用 Nitro 框架：

```
backend-mock/
├── api/                 # API 路由定义
│   ├── auth/           # 认证相关 API
│   ├── basic-static-tables/  # 基础静态表格 API
│   ├── system/         # 系统管理 API
│   └── ...             # 其他业务模块 API
├── middleware/         # 中间件
├── utils/              # 工具函数
└── routes/             # 通用路由处理
```

### API 路由规范

#### 1. 文件命名规范
- GET 请求：`filename.get.ts`
- POST 请求：`filename.post.ts`
- PUT 请求：`filename.put.ts`
- DELETE 请求：`filename.delete.ts`
- 通用处理：`filename.ts` 或 `index.ts`

#### 2. API 路由示例
```typescript
// apps/backend-mock/api/system/users.get.ts
export default defineEventHandler(async (event) => {
  const query = getQuery(event);
  
  // 参数验证
  const { page = 1, pageSize = 10, keyword } = query;
  
  // 模拟数据处理
  const users = mockUsers.filter(user => 
    !keyword || user.name.includes(keyword as string)
  );
  
  // 分页处理
  const total = users.length;
  const start = (Number(page) - 1) * Number(pageSize);
  const end = start + Number(pageSize);
  const data = users.slice(start, end);
  
  return {
    code: 0,
    message: 'success',
    data: {
      records: data,
      total,
      current: Number(page),
      size: Number(pageSize),
    }
  };
});
```

#### 3. 认证中间件
使用 [apps/backend-mock/middleware/1.api.ts](mdc:apps/backend-mock/middleware/1.api.ts) 进行统一认证处理：

```typescript
export default defineEventHandler(async (event) => {
  const url = getRequestURL(event);
  
  // 跳过认证的路径
  if (skipAuthPaths.includes(url.pathname)) {
    return;
  }
  
  // 验证 token
  const token = getCookie(event, 'accessToken') || getHeader(event, 'authorization');
  
  if (!token || !verifyToken(token)) {
    throw createError({
      statusCode: 401,
      statusMessage: 'Unauthorized'
    });
  }
});
```

### 工具函数使用

#### 1. JWT 工具 ([apps/backend-mock/utils/jwt-utils.ts](mdc:apps/backend-mock/utils/jwt-utils.ts))
```typescript
import { generateToken, verifyToken } from '~/utils/jwt-utils';

// 生成 token
const token = generateToken({ userId: user.id, username: user.username });

// 验证 token
const payload = verifyToken(token);
```

#### 2. Cookie 工具 ([apps/backend-mock/utils/cookie-utils.ts](mdc:apps/backend-mock/utils/cookie-utils.ts))
```typescript
import { setTokenCookies, clearTokenCookies } from '~/utils/cookie-utils';

// 设置认证 Cookie
setTokenCookies(event, { accessToken, refreshToken });

// 清除认证 Cookie
clearTokenCookies(event);
```

#### 3. 模拟数据 ([apps/backend-mock/utils/mock-data.ts](mdc:apps/backend-mock/utils/mock-data.ts))
```typescript
import { generateMockData } from '~/utils/mock-data';

// 生成模拟用户数据
const users = generateMockData('users', 100);

// 生成模拟列表数据
const list = generateMockData('list', { count: 50, type: 'system' });
```

## 前端 API 调用

### API 配置结构
前端 API 配置位于 [apps/web-antd/src/api](mdc:apps/web-antd/src/api)：

```
api/
├── core/              # 核心 API 配置
│   ├── auth.ts       # 认证 API
│   ├── request.ts    # 请求封装
│   └── types.ts      # API 类型定义
├── system/           # 系统管理 API
└── ...               # 其他业务模块 API
```

### API 接口定义规范

#### 1. 接口类型定义
```typescript
// 通用响应类型
export interface ApiResponse<T = any> {
  code: number;
  message: string;
  data: T;
}

// 分页响应类型
export interface PageResult<T = any> {
  records: T[];
  total: number;
  current: number;
  size: number;
}

// 查询参数类型
export interface QueryParams {
  page?: number;
  pageSize?: number;
  keyword?: string;
  [key: string]: any;
}
```

#### 2. API 接口实现
```typescript
// apps/web-antd/src/api/system/user.ts
export interface UserApi {
  getList: (params: QueryParams) => Promise<PageResult<User>>;
  getById: (id: string) => Promise<User>;
  create: (data: CreateUserDto) => Promise<void>;
  update: (id: string, data: UpdateUserDto) => Promise<void>;
  delete: (id: string) => Promise<void>;
}

export const userApi: UserApi = {
  getList: (params) => requestClient.get('/api/system/users', { params }),
  getById: (id) => requestClient.get(`/api/system/users/${id}`),
  create: (data) => requestClient.post('/api/system/users', data),
  update: (id, data) => requestClient.put(`/api/system/users/${id}`, data),
  delete: (id) => requestClient.delete(`/api/system/users/${id}`),
};
```

### 请求封装

#### 1. 请求客户端配置 ([apps/web-antd/src/api/request.ts](mdc:apps/web-antd/src/api/request.ts))
```typescript
import { useAuthStore } from '#/store';

export const requestClient = createRequestClient({
  baseURL: import.meta.env.VITE_API_URL,
  
  // 请求拦截器
  onRequest: (config) => {
    const authStore = useAuthStore();
    if (authStore.accessToken) {
      config.headers.Authorization = `Bearer ${authStore.accessToken}`;
    }
    return config;
  },
  
  // 响应拦截器
  onResponse: (response) => {
    const { code, message, data } = response.data;
    
    if (code !== 0) {
      throw new Error(message || '请求失败');
    }
    
    return data;
  },
  
  // 错误处理
  onError: (error) => {
    if (error.response?.status === 401) {
      // 处理认证失败
      const authStore = useAuthStore();
      authStore.logout();
    }
    
    throw error;
  },
});
```

#### 2. API 使用示例
```typescript
// 在组件中使用
import { userApi } from '#/api/system/user';

// 获取用户列表
const { data: users, loading } = await userApi.getList({
  page: 1,
  pageSize: 10,
  keyword: 'admin'
});

// 创建用户
try {
  await userApi.create({
    username: 'newuser',
    email: '<EMAIL>'
  });
  message.success('创建成功');
} catch (error) {
  message.error(error.message || '创建失败');
}
```

## 认证流程

### 1. 登录认证
```typescript
// apps/backend-mock/api/auth/login.post.ts
export default defineEventHandler(async (event) => {
  const { username, password } = await readBody(event);
  
  // 验证用户名密码
  const user = await validateUser(username, password);
  
  if (!user) {
    throw createError({
      statusCode: 401,
      statusMessage: 'Invalid credentials'
    });
  }
  
  // 生成 token
  const accessToken = generateToken({ userId: user.id });
  const refreshToken = generateRefreshToken({ userId: user.id });
  
  // 设置 Cookie
  setTokenCookies(event, { accessToken, refreshToken });
  
  return {
    code: 0,
    message: 'success',
    data: {
      user,
      accessToken,
      refreshToken
    }
  };
});
```

### 2. 权限验证
```typescript
// 权限验证中间件
export function requirePermission(permission: string) {
  return defineEventHandler(async (event) => {
    const user = await getCurrentUser(event);
    
    if (!user.permissions.includes(permission)) {
      throw createError({
        statusCode: 403,
        statusMessage: 'Insufficient permissions'
      });
    }
  });
}
```

## 错误处理规范

### 1. 后端错误处理
```typescript
export default defineEventHandler(async (event) => {
  try {
    // API 业务逻辑
    return successResponse(data);
  } catch (error) {
    // 统一错误处理
    return errorResponse(error.message || '服务器错误', error.code || 500);
  }
});
```

### 2. 前端错误处理
```typescript
// 全局错误处理
app.config.errorHandler = (err, vm, info) => {
  console.error('全局错误:', err);
  message.error('系统错误，请联系管理员');
};

// API 错误处理
try {
  const result = await api.call();
  return result;
} catch (error) {
  if (error.response?.status === 400) {
    message.error('请求参数错误');
  } else if (error.response?.status === 500) {
    message.error('服务器错误');
  } else {
    message.error(error.message || '未知错误');
  }
  throw error;
}
```

## 开发调试

### 1. API 调试
- 使用浏览器开发者工具 Network 面板
- 使用 Postman 或类似工具测试 API
- 查看 Nitro 服务器控制台日志

### 2. Mock 数据更新
- 模拟数据配置在 [apps/backend-mock/utils/mock-data.ts](mdc:apps/backend-mock/utils/mock-data.ts)
- 可以根据需要调整数据结构和数量
- 支持动态生成不同类型的测试数据

### 3. 环境配置
- 开发环境：使用 mock 服务
- 生产环境：连接真实后端 API
- 通过环境变量 `VITE_API_URL` 控制 API 基础地址
