var parentLims;
$(function () {
    getGreeting();
    init();
    $(".close").click(function (event) {
        $("#el-dialog").addClass("hide");
    });
    parentLims =  window.parent.lims;


})
function init() {

    getAndDispPieData();
    
    getAndDispHistogram();

    getAndDispHistogramBS();

    window.addEventListener("resize", (event) => {
        pieChart1.resize();
        histogramChart.resize();
        histogramChart2.resize();
    });
    var resData = getRutData();
    var rutData = resData.RutData;
    var userData = resData.UserData;
    var todoData =  resData.TodoData;

    dispRutData(rutData);
    dispUserData(userData);
    dispTodoData(todoData);
   

    document.getElementById('rut_ul').addEventListener('click', function(event) {
        var target = event.target;
        if (target) {
            if(target.nodeName !== 'LI')
            {
                target = target.parentNode;
            }
            if (target.nodeName === 'LI') {
                //显示详细内容弹窗
                //1、获取data-id属性值
                let dataId = target.getAttribute("data-id");
                if(dataId)
                {
                    //2、根据id查找内容详细内容
                    let findRes = rutData.find( d => d.Id == dataId);
                    if(findRes)
                    {
                        //3、显示弹窗
                        document.getElementById('reportTitle').innerHTML = findRes.Title;
                        document.getElementById('el-dialog_body').innerHTML = findRes.Content;
                        document.getElementById('el-dialog').classList.remove('hide');
                    }
                }
                
            }

        }
    });
}

function getGreeting() {
    today = new Date();
    var day; var date; var hello; var wel;
    hour = new Date().getHours()

    if (hour < 9) hello = '早上好，充满活力的一天开始了！'
    else if (hour < 12) hello = '上午好，充满活力的一天！'
    else if (hour < 14) hello = '中午好，热情如火的中午时分！'
    else if (hour < 18) hello = '下午好！'
    else if (hour < 24) hello = '晚上好，充满神秘色彩的夜晚的开始了！'

    if (today.getDay() == 0) day = '星期日'
    else if (today.getDay() == 1) day = '星期一'
    else if (today.getDay() == 2) day = '星期二'
    else if (today.getDay() == 3) day = '星期三'
    else if (today.getDay() == 4) day = '星期四'
    else if (today.getDay() == 5) day = '星期五'
    else if (today.getDay() == 6) day = '星期六'

    date = (today.getFullYear()) + '年' + String((today.getMonth() + 1)).padStart(2, '0') + '月' + String(today.getDate()).padStart(2, '0') + '日' + String(hour).padStart(2, '0') + '时';
    document.getElementById('greet').innerHTML = (hello + '<br>' + date + ' ' + day);
}

//获取用户及通知信息
function getRutData()
{
    var data = request("Welcome.getMsg");
    if(data == null)
    {
        return [];
    }
    return JSON.parse(data);
}


//跳转新页面
function openOnTop(s)
{
    var sUrl = top.location.href + s;
    window.open(sUrl, "_blank");
}

//请求服务器数据
function request(scriptName, params)
{
    // debugger
    if(!parentLims)
    {
        parentLims = window.parent.lims;
    }
    // console.log(parentLims);
    if(scriptName == null || scriptName.trim().length <= 0)
    {
        return;
    }
    return parentLims.CallServer(scriptName);
}

//渲染通知列表
function dispRutData(rutData)
{
    for(let rd of rutData)
    {
        var newLi = document.createElement('li');
        var newA = document.createElement('a');
        newA.innerHTML = rd.Title;
        
        var newSpan = document.createElement('span');
        newSpan.innerHTML = rd.RutDate;
        newLi.setAttribute("data-id", rd.Id);
        newLi.append(newA);
        newLi.append(newSpan);
        $("#rut_ul").append(newLi);
    }
}


//渲染用户信息
function dispUserData(userData)
{
    $('#lastLog').text(userData.SuccDate);
    $('#pwdExpir').text(userData.PwdEptDate);
    $('#lastUpdPwd').text(userData.PwdChDate);
    $('#dbUsr').text(userData.FullName);
    $('#dbDept').text( userData.Site );
    $('#dbRole').text( userData.Role );
}

//渲染任务信息
function dispTodoData(todoData)
{
    $('#dbMyTask').text(todoData[0]);
    $('#dbMySvgTask').text(todoData[1]);

    $('#dbMySvgApprTask').text( todoData[2] );
}

//获取渲染各组检验中任务饼图信息
function getAndDispPieData()
{
    var taskData = request("Welcome.getSvgTask");
    var svgList = taskData.map(t => t.name);

    var pieChart1 = echarts.init(document.getElementById('pieChart1'));
    pieChart1.setOption({
        //每个组的显示颜色
        color: ["#87cefa", "#ff7f50", "#32cd32", "#da70d6", "#454eee", "#60628d", "#ecd92b"],
        legend: {
            y: '260',
            x: 'center',
            textStyle: {
                color: 'black',
                fontSize: 16
            },
            //每个组的名称
            data: svgList,
        },
        tooltip: {
            trigger: 'item',
            formatter: "{a}<br/>{b}<br/>{c}({d}%)"
        },
        calculable: false,
        series: [
            {
                name: '检验任务数量',
                type: 'pie',
                radius: ['40%', '70%'],
                center: ['50%', '45%'],
                itemStyle: {
                    normal: {
                        label: {
                            show: false
                        },
                        labelLine: {
                            show: false
                        }
                    },
                    emphasis: {
                        label: {
                            show: true,
                            position: 'center',
                            textStyle: {
                                fontSize: '20',
                                fontWeight: 'bold'
                            }
                        }
                    }
                },
                //每个组的检验任务数量
                data: taskData
            }
        ]
    });
}

//获取总批次检验数据信息
function getAndDispHistogram()
{
    var aBatchData = request("Welcome.getBatchTolSum");
    var aType = aBatchData.map(a => a[0]);

    var aSeries = aBatchData.map(a =>{
        return {
            name: a[0],
            type: 'bar',
            stack: '总量',
            itemStyle: { normal: { label: { show: true, position: 'insideRight' } } },
            data: [a[1], a[2], a[3]]
        }
    })
    

    var histogramChart = echarts.init(document.getElementById('histogramChart'));
    histogramChart.setOption({
        //类别对应颜色
        color: ["#87cefa", "#ff7f50", "#32cd32", "#da70d6", "#454eee", "#60628d", "#ecd92b", "#80e72b", "#56a118", "#c72158"],
        legend: {
            y: '250',
            x: 'center',
            //类别
            data: aType,
            textStyle: {
                color: 'black',
                fontSize: 16
            }
        },
        calculable: false,
        grid: {
            left: '5%',
            right: '5%',
            bottom: '20%',
            containLabel: true
        },
        tooltip: {
            trigger: 'axis',
            axisPointer: {
                type: 'shadow'
            }
        },
        xAxis: [
            {
                type: 'value',
                axisLabel: {
                    show: true,
                    textStyle: {
                        color: 'black'
                    }
                },
                splitLine: {
                    lineStyle: {
                        color: ['#f2f2f2'],
                        width: 0,
                        type: 'solid'
                    }
                }

            }
        ],
        yAxis: [
            {
                type: 'category',
                //展示数据列表
                data: ['总批次检验数量', '去年批次检验数量', '本年批次检验数量'],
                axisLabel: {
                    show: true,
                    textStyle: {
                        color: 'black',
                        fontSize: 16
                    }
                },
                splitLine: {
                    lineStyle: {
                        width: 0,
                        type: 'solid'
                    }
                }
            }
        ],
        //各类别对应展示数据列表数据
        series: aSeries
    });
}

//获取批次数据状态统计
function getAndDispHistogramBS()
{
    var aBatchData = request("Welcome.getTesingBatch");
    //状态列表
    let statusLs = aBatchData.map(a => a[0]);
    //各流程状态数据量
    let sideData = aBatchData.map(a => a[1]);
  

    var histogramChart2 = echarts.init(document.getElementById('histogramChart2'));
   
    var option = {
        tooltip: {
            trigger: 'axis',
            axisPointer: {
                type: 'shadow'
            }
        },
        legend: {
            data: [
                {
                    name: "检验中批次流程状态统计",
                    textStyle: {
                        color: 'white'
                    }
                }
            ]
        },
        grid: {
            left: '5%',
            right: '5%',
            bottom: '20%',
            containLabel: true
        },
        toolbox: {
            show: true,
        },
        calculable: true,
        xAxis: [
            {
                type: 'category',
                splitLine: {
                    show: false
                },
                //流程状态列表
                data: statusLs,
                axisLabel: {
                    show: true,
                    textStyle: {
                        color: "black",
                        fontSize: 16
                    },
                },
            }
        ],
        yAxis: [
            {
                type: 'value',
                splitLine: {
                    show: false
                },
                axisLabel: {
                    show: true,
                    textStyle: {
                        color: "#black",
                        fontSize: 16
                    },
                },
            }
        ],
        series: [
            {
                name: '数量',
                tooltip: {
                    show: true
                },
                type: 'bar',
                barWidth:'35%',
                itemStyle: {
                    normal: {
                        color:'#13227a',
                        opacity: 1,
                        barBorderRadius: 5,
                    }
                },
                data: sideData,
                barGap: 0,
            }]
    };
    histogramChart2.setOption(option);
}