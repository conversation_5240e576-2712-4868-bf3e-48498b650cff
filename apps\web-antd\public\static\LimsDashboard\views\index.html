<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <title>Dashboard</title>
    <link href="../styles/common.css" rel="stylesheet">
    <script src="../scripts/Plugin/jquery-3.3.1.min.js"></script>
    <script src="../scripts/Plugin/echarts.min.js"></script>
    <script src="../scripts/index.js"></script>
</head>

<body>
    <!--内容部分-->
    <div class="con left">
        <div class="div_any">
            <!--左侧用户信息，通知-->
            <div class="left div_any01">
                <!--用户信息-->
                <div class="user_center left_card">
                    <div id="greet"></div>
                    <div class="user_box">
                        <div class="userImg left">
                            <img src="../images/avatar_2.png" />
                        </div>
                        <div class="user_con left">
                            <div id="dbUsr" class="user_name">
                                用户名
                            </div>
                            <div class="user_role">
                                <span id="dbDept">实验室</span>
                                <span>*</span>
                                <span id="dbRole"> 角色</span>
                            </div>
                        </div>
                    </div>
                    <hr>
                    <div class="user_pri color_font">
                        <ul>
                            <li>
                                上次登录成功时间：<span id="lastLog"></span>
                            </li>
                            <li>
                                账号密码到期时间：<span id="pwdExpir"></span>
                            </li>
                            <li>
                                上次密码修改时间：<span id="lastUpdPwd"></span>
                        </ul>
                    </div>
                </div>
                <!--通知-->
                <div class="rut-tit1">公告</div>
                <div class="mes-rut">
                    <div id="scrollDiv">
                        <ul id="rut_ul">
                        </ul>
                    </div>
                </div>
                <!--常用功能-->
                <div class="common_app left_card">
                    <div class="rut-tit1">常用功能</div>
                    <div class="app_box">
                        <div class="app_item left" onclick="openOnTop('?FormId=DilutionManagement.MAIN&formargs=%5B%22SolutionConfiguration%22%2C%22Draft%22%2C%22%20and%20STATUS%20%3D%20%3F%22%2C%22%27Draft%27%22%5D')">
                            <img src="../images/rongye.png">
                            <div>溶液配制</div>
                        </div>
                        <div class="app_item left" onclick="openOnTop('?FormId=MediumManager.Main&formargs=%5B%22MediumManager%22%2C%22Draft%22%2C%22%20and%20STATUS%20%3D%20%3F%22%2C%22%27Draft%27%22%5D')">
                            <img src="../images/peiyangji.png">
                            <div>培养基配制</div>
                        </div>
                        <div class="app_item left" onclick="openOnTop('?FormId=ObjManagement.ObjManagement&formargs=%5B%22CellManager%22%2C%22Draft%22%5D')">
                            <img src="../images/xibao.png">
                            <div>细胞管理</div>
                        </div>
                        <div class="app_item left" onclick="openOnTop('?FormId=StandardMaterialManage.MainForm')">
                            <img src="../images/shiji.png">
                            <div>实验室耗材管理</div>
                        </div>
                        <div class="app_item left" onclick="openOnTop('?FormId=BatchManager.BatchManager&formargs=%5B%22View%22%5D')">
                            <img src="../images/xinxi.png">
                            <div>查看批次</div>
                        </div>
                        <div class="app_item left" onclick="openOnTop('?FormId=FungusManage.MainForm&formargs=%5B%22Manager%22%5D')">
                            <img src="../images/junzhong.png">
                            <div>菌种管理</div>
                        </div>
                    </div>
                </div>
            </div>
            <!--右侧统计-->
            <div class="right div_any02">
                <div class="con_div left_card">
                    <div class="con_div_text left">
                        <div class="con_div_text01 left todo_card01">
                            <img src="../images/info_1.png" class="left text01_img" />
                            <div class="left text01_div" onclick="openOnTop('?FormId=RpsWorkAssignment.rpsWorkAssignment&formargs=%5B%22MYTODOLIST%22%5D')">
                                <p>我的任务</p>
                                <p id="dbMyTask">0</p>
                            </div>
                        </div>
                    </div>
                    <div class="con_div_text left">
                        <div class="con_div_text01 left todo_card02">
                            <img src="../images/info_3.png" class="left text01_img" />
                            <div class="left text01_div" onclick="openOnTop('?FormId=RUNBUILD_RESENT_APPROVE.MAINFORM&formargs=%5B%22MYTEAMSASSIGNMENTS%2CRESULTS%22%5D')">
                                <p>同组任务</p>
                                <p id="dbMySvgTask">0</p>
                            </div>
                        </div>
                    </div>
                    <div class="con_div_text left">
                        <div class="con_div_text01 left todo_card03">
                            <img src="../images/info_5.png" class="left text01_img" />
                            <div class="left text01_div" onclick="openOnTop('?FormId=RUNBUILD_RESENT_APPROVE.MAINFORM&formargs=%5B%22APPROVAL%2CAPPROVAL%22%5D')">
                                <p>同组复核</p>
                                <p id="dbMySvgApprTask">0</p>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="div_any_child">
                    <div class="div_any_title"><img src="../images/title_1.png">各岗位检验任务数据量</div>
                    <p id="pieChart1" class="p_chart"></p>
                </div>
                <div class="div_any_child">
                    <div class="div_any_title"><img src="../images/title_10.png">批次检验数据统计</div>
                    <p id="histogramChart" class="p_chart"></p>
                </div>
                <div class="div_any_child">
                    <div class="div_any_title"><img src="../images/title_10.png">检验中批次流程状态统计</div>
                    <p id="histogramChart2" class="p_chart"></p>
                </div>

            </div>
        </div>

        <div id="el-dialog" class="hide">
            <div class="xc_layer"></div>
            <div class="popBox" id="printView">
                <div class="ttBox">
                    <span class="tt" id="reportTitle">标题</span>
                    <img src="../images/guanbi.png" style="width: 30px;float: right;cursor: pointer;" title="关闭弹窗"
                        class="close" />
                </div>
                <div class="txtBox" id="el-dialog_body">
                </div>
            </div>
        </div>

    </div>
</body>
<script>
    
</script>
</html>