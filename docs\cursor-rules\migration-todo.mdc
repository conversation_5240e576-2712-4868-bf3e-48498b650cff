# Cursor Rule: 组件构建指南（阶段 B）

## 目标

- **目的**: 面向原 LIMS 页面“可见元素”的还原与复刻，提供在现有前端（Vue3 + Ant Design Vue + Vben）中的组件化落地指南。
- **定位**: 只关注组件构建与接入（列表、弹窗、按钮、状态控件、标签页等），不讨论业务迁移策略。
- **参考目录**: `apps/web-antd/src/views/system`（仅用于示例与规范锚定）。

## 输入材料（用于组件还原）

- 原系统页面截图
- 原系统前端代码
- 原系统前端元素 XML

---

## 路由接入规范

- 路由模块位置：`apps/web-antd/src/router/routes/modules/system.ts`
- 子页面按以下示例添加路由项（标题使用国际化键 `$t('system.xxx.title')`）：

```ts
// 路由示例（添加到 system.ts 的 children 中）
{
  path: '/system/example',
  name: 'SystemExample',
  meta: { icon: 'lucide:file', title: $t('system.example.title') },
  component: () => import('#/views/system/example/list.vue'),
}
```

- 根节点 `System` 已定义图标/排序/标题；保持与现有结构一致。

---

## 国际化接入规范

- 语言包目录：`apps/web-antd/src/locales/langs/{zh-CN|en-US}/system.json`
- 入口：`apps/web-antd/src/locales/index.ts` 已配置 `$t` 与语言包自动加载
- 键名建议：
  - 页面标题：`system.xxx.title`
  - 列字段：`system.xxx.columns.xxx`
  - 搜索字段：`system.xxx.search.xxx`
  - 表单字段：`system.xxx.form.xxx`
  - 操作与提示：`system.xxx.action.xxx`、`system.xxx.message.xxx`

示例（`zh-CN/system.json`）：

```json
{
  "title": "系统管理",
  "example": {
    "title": "示例页面",
    "columns": { "name": "名称", "code": "编码", "status": "状态" },
    "search": { "keyword": "关键字", "status": "状态" },
    "form": { "name": "名称", "code": "编码" },
    "action": { "create": "新建", "edit": "编辑", "delete": "删除" }
  }
}
```

---

## 组件构建通用规范

### 文件结构（标准 CRUD 模式）

- `list.vue`: 主列表页（含工具栏与 Grid）
- `data.ts`: 表格列、搜索表单 Schema、表单 Schema
- `modules/form.vue`: 创建/编辑弹层（Drawer/Modal）
- 可选：`modules/detail.vue`、`modules/*-form.vue`、`tabs/*`

### 列表页（list.vue）

- 使用 `useVbenVxeGrid` 创建 `Grid` 与 `gridApi`
- 工具栏按钮：通过 `toolbarConfig.slots.buttons` 自定义；按钮接入权限与工作流校验
- 查询：如需搜索，才设置 `formOptions`（按需使用，不是必须）
- 数据代理：`proxyConfig.ajax.query` 对接后端，返回 `{ items, total }`
- 行操作：`CellOperation` 统一处理 `edit/delete/view/...`
- 行主键：`rowConfig.keyField` 设置为实际主键字段
- Grid 必选配置：`keepSource`、`toolbarConfig`、`editConfig`、`menuConfig`
- 可选配置：`rowGroupConfig`（如需分组展示）
- `params`（`limsControlId`、`tableName`）可以从旧系统 XML 中查找对应控件与数据表名

VbenVxeGrid 配置要点（推荐模板）：

```vue
<script lang="ts" setup>
import type { VxeGridDefines } from 'vxe-table/types/all';
import { Page, useVbenDrawer } from '@vben/common-ui';
import { Button, Space, message } from 'ant-design-vue';
import { useVbenVxeGrid } from '#/adapter/vxe-table';
import { useWorkflowPermission } from '#/hooks/useWorkflowPermission';
import { $t } from '#/locales';
import { saveEditingRowOriginalData } from '#/utils/lims-grids-config';
import { useColumns } from './data';
import { updateProvider } from '#/api';

const { workflowGranted } = useWorkflowPermission();

const [Grid, gridApi] = useVbenVxeGrid({
  // 按需：仅当需要搜索时才设置
  // formOptions: { schema: useGridFormSchema(), submitOnChange: true },

  gridEvents: {
    // 常用三事件：currentRowChange、editActivated、editClosed
    currentRowChange: async (params: VxeGridDefines.CurrentRowChangeParams) => {
      // TODO: 选中行联动其他 Grid 或加载详情
    },
    editActivated: saveEditingRowOriginalData,
    editClosed: updateProvider,
  },

  gridOptions: {
    columns: useColumns(),
    height: 'auto',
    keepSource: true,
    rowConfig: { keyField: 'ORIGREC', isCurrent: true, isHover: true },

    // 可选：分组展示
    // rowGroupConfig: { indent: 100, mode: 'column', groupFields: ['CATEGORY'] },

    toolbarConfig: {
      slots: { buttons: 'toolbarButtons' },
      custom: true,
      export: true,
      refresh: { code: 'query' },
      search: true,
      zoom: true,
    },

    editConfig: { trigger: 'click', mode: 'cell', enabled: false },

    menuConfig: {
      body: {
        options: [[{ code: 'viewAudit', name: '查看历史', prefixConfig: { icon: 'vxe-icon-table' } }]],
      },
    },

    params: {
      // 从旧 XML 中对应控件 Id 与表名提取
      limsControlId: 'dgdTableList',
      tableName: 'TABLEDESIGN',
    },

    proxyConfig: {
      ajax: {
        query: async () => ({ items: [], total: 0 }),
      },
    },
  },
});
</script>

<template>
  <Page auto-content-height>
    <Grid>
      <template #toolbarButtons>
        <Space>
          <Button type="primary" v-access:code="['System.Example.Create']" v-if="workflowGranted('System.Example.Create')">
            {{ $t('system.example.action.create') }}
          </Button>
        </Space>
      </template>
    </Grid>
  </Page>
</template>
```

### Grid 布局规范

- 使用 `toolbarButtons` 插槽渲染工具栏按钮
- 需要上下或左右分割布局时，使用 `ResizablePanelGroup` + `ResizablePanel` + `ResizableHandle`

示例：

```vue
<ResizablePanelGroup direction="horizontal">
  <ResizablePanel :default-size="50">
    <TopGrid>
      <template #toolbarButtons>
        <Space>
          <Button type="primary">{{ $t('system.example.action.create') }}</Button>
        </Space>
      </template>
    </TopGrid>
  </ResizablePanel>
  <ResizableHandle with-handle />
  <ResizablePanel collapsible :collapsed-size="50">
    <BottomGrid />
  </ResizablePanel>
  
</ResizablePanelGroup>
```

### 数据配置（data.ts）

- 搜索 Schema：`useGridFormSchema()` 描述筛选字段
- 表格列：`useColumns(onActionClick, onStatusChange?)`
- 表单 Schema：`useFormSchema(formDataRef)`，覆盖上传、字典、依赖显示

片段：

```ts
import type { Ref } from 'vue';
import type { VbenFormSchema } from '#/adapter/form';
import type { OnActionClickFn, VxeTableGridOptions } from '#/adapter/vxe-table';
import { $t } from '#/locales';

export function useGridFormSchema(): VbenFormSchema[] {
  return [ { component: 'Input', fieldName: 'keyword', label: $t('system.example.search.keyword') } ];
}

export function useColumns(onActionClick: OnActionClickFn<any>): VxeTableGridOptions['columns'] {
  return [
    { field: 'name', title: $t('system.example.columns.name'), minWidth: 120 },
    { field: 'code', title: $t('system.example.columns.code'), minWidth: 120 },
    { field: 'operation', title: $t('commons.action'), fixed: 'right', width: 130, align: 'center', cellRender: { name: 'CellOperation', attrs: { onClick: onActionClick } } },
  ];
}

export function useFormSchema(_formData: Ref<any>): VbenFormSchema[] {
  return [
    { component: 'Input', fieldName: 'name', label: $t('system.example.form.name'), rules: 'required' },
    { component: 'Input', fieldName: 'code', label: $t('system.example.form.code'), rules: 'required' },
  ];
}
```

### 表单弹层（modules/form.vue）

- `useVbenForm` + `useVbenDrawer` 组合
- `onConfirm`: 校验、取值、接口提交、锁定与反馈
- 文件上传：处理 `beforeUpload` 与回显（如需）

片段：

```vue
<script lang="ts" setup>
import type { Ref } from 'vue';
import { computed, ref } from 'vue';
import { useVbenDrawer } from '@vben/common-ui';
import { useVbenForm } from '#/adapter/form';
import { $t } from '#/locales';
import { useFormSchema } from '../data';

const emits = defineEmits(['success']);
const formData = ref<any>();
const [Form, formApi] = useVbenForm({ schema: useFormSchema(formData as Ref<any>), showDefaultActions: false });

const [Drawer, drawerApi] = useVbenDrawer({
  async onConfirm() {
    const { valid } = await formApi.validate();
    if (!valid) return;
    const values = await formApi.getValues();
    drawerApi.lock();
    try { /* await save(values) */ emits('success'); drawerApi.close(); } finally { drawerApi.unlock(); }
  },
  onOpenChange(isOpen) {
    if (isOpen) {
      const data = drawerApi.getData<any>();
      formApi.resetForm();
      formData.value = data || undefined;
      if (data) formApi.setValues(data);
    }
  },
});

const getTitle = computed(() => formData.value?.id ? $t('ui.actionTitle.edit', [$t('system.example.title')]) : $t('ui.actionTitle.create', [$t('system.example.title')]));
</script>

<template>
  <Drawer :title="getTitle">
    <Form />
  </Drawer>
</template>
```

### 权限与工作流

- 访问权限：`v-access:code="[permissionCode]"`
- 工作流权限：`workflowGranted(code)`；也可使用 `workflowGrantedAny/All`

---

## 定义完毕（Definition of Done）

- 组件文件齐备：`list.vue`、`data.ts`、`modules/form.vue`（必要时含 `tabs`/`detail`）
- 路由项添加至 `router/routes/modules/system.ts`，使用 `$t('system.xxx.title')`
- 国际化键添加至 `locales/langs/zh-CN/system.json` 与 `en-US/system.json`
- 权限 `v-access:code` 与 `workflowGranted` 接入
- 关键交互具备确认与消息提示；表单校验完整
- 通过构建与基础自测
