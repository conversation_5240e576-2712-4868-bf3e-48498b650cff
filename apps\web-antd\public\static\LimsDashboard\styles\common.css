body::-webkit-scrollbar {
    display: none;
}

* {
    margin: 0;
    padding: 0;
    list-style: none;
    text-decoration: none;
}

/* CSS Document */
::-webkit-scrollbar-track {
    border-radius: 10px;
    -webkit-box-shadow: inset 0 0 6px rgba(0, 0, 0, 0);
}

/*滚动条的滑轨背景颜色*/
::-webkit-scrollbar-thumb {
    background-color: rgba(0, 0, 0, 0.05);
    border-radius: 10px;
    -webkit-box-shadow: inset 1px 1px 0 rgba(75, 75, 75, 0.58);
}

/*滑块颜色*/
::-webkit-scrollbar-thumb {
    background-color: rgba(0, 0, 0, 0.2);
    border-radius: 10px;
    -webkit-box-shadow: inset 1px 1px 0 rgba(48, 48, 48, 0.92);
}

::-webkit-scrollbar {
    width: 16px;
    height: 16px;
}

/* 滑块整体设置*/
::-webkit-scrollbar-track,
::-webkit-scrollbar-thumb {
    border-radius: 999px;
    border: 5px solid transparent;
}

::-webkit-scrollbar-track {
    box-shadow: 1px 1px 5px rgba(0, 0, 0, .2) inset;
}

::-webkit-scrollbar-thumb {
    min-height: 20px;
    background-clip: content-box;
    box-shadow: 0 0 0 5px rgba(255, 255, 255, .5) inset;
}

::-webkit-scrollbar-corner {
    background: transparent;
}

html {
    height: 100%;
    margin: 0;
}

body {
    margin: 0;
    height: 100%;
    background-color: #dce1e7;
}

a:hover {
    text-decoration: none;
}

.left {
    float: left;
}

.right {
    float: right;
}

.clear {
    clear: both;
}

.text_right {
    text-align: right;
}

.left_card {
    margin: 5px auto;
}

#greet {
    margin-bottom: 16px !important;
    font-size: 22px !important;
    font-weight: bold;
}

.user_center {
    width: 100%;
    text-align: center;
    height: 30%;
    min-height: 300px;
    
    background-size: 100% 100%;
    font-family: "微软雅黑" !important;
    /* background-color: #13227a;
    color: white; */
    background-color: white;
    color: #0b64a3;
}

.user_center h2 {
    margin-top: 16px !important;
    margin-bottom: 16px !important;
    font-size: 22px !important;
}

.user_box {
    height: 110px;
}

.user_name {
    font-size: 22px !important;
    font-weight: bold;
    margin: 15px 30px;

}

.userImg {
    width: 100px;
    height: 100px;
    border-radius: 50%;
    overflow: hidden;
    margin-left: 15px;
    background-color: white;
    /* box-shadow: 0 0 15px #1eff00;  */
}

.userImg img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.user_con {
    margin-left: 15px;
    text-align: left;
}

.user_role {
    margin-left: 30px;
}

.user_role span {
    font-size: 15px !important;
    margin: 2px;
}

.color_font {
    color: gray !important;
    font-size: 12px !important;
}

.header_logo {
    margin-left: 1%;
    margin-top: 12px;

}

.user_pri>ul {
    margin-left: 15px;
}

.user_pri>ul>li {
    width: 100%;
    text-align: left;
    height: 20px;
    position: relative;
    line-height: 15px;
    margin-top: 15px;
    box-sizing: border-box;
    border-radius: 5px;
    color: #0b64a3;
}

.user_pri>ul>li span {
    margin-left: 30px;
}

.mes-rut {
    background: white;
    text-align: left;
    height: 22%;
    overflow-y: scroll;
}

.rut-tit1 {
    padding-left: 20px;
    font-size: 22px;
    font-weight: bold;
    background-color: white;
}

#scrollDiv {
    margin: 10px 25px;
}

#scrollDiv ul li {
    height: 38px;
    overflow: hidden;
    margin-bottom: 4px;
    font-size: 14px;
    padding: 0 9px 0 9px;
    line-height: 30px;
    color: #0b64a3;
}


#scrollDiv ul li a {
    display: block;
    line-height: 18px;
    float: left;
    width: 80%;
}

#scrollDiv ul li a:hover {
    display: block;
    line-height: 18px;
    float: left;
    width: 80%;
    cursor: pointer;
}

#scrollDiv ul li span {
    bottom: 5px;
    right: 16px;
    display: block;
    text-align: center;
    color: gray;
    line-height: 18px;
    float: right;
}


.common_app {
    background: white;
    text-align: left;
    overflow: hidden;
    height: 44%;
}

.app_box {
    display: flex;
    flex-wrap: wrap;
    justify-content: left;
}

.app_item {
    height: 80px;
    width: 80px;
    margin: 20px;
    
    cursor: pointer;
    box-sizing: border-box;
    position: relative;
}

.app_item img {
    position: absolute;
    /* 设置绝对定位 */
    width: 100%;
    height: 100%;
}

.app_item div {
    text-align: center;
    margin-top: 80px;
    color: #0b64a3;
}

/* #4629c3 */
.todo_card01 {
    /* background-image: url("../images/cardbg_01.png");
    background-repeat: no-repeat;
    background-size: 100% 100%; */
    background-color: #4629c3;
}

/* #3317e6 */
.todo_card02 {
    /* background-image: url("../images/cardbg_02.png");
    background-repeat: no-repeat;
    background-size: 100% 100%; */
    background-color: #2c17b6;
}
/* #13227a */
.todo_card03 {
    /* background-image: url("../images/cardbg_03.png");
    background-repeat: no-repeat;
    background-size: 100% 100%; */
    background-color: #13227a;
}

.con {
    width: 100%;
    height: 100%;
    background-color: width;
    /* padding-top: 20px;
    padding-bottom: 20px; */
}


.con_div {
    height: 20%;
    min-height: 100px;
    max-height: 150px;
    width: 100%;
    padding: 5px 15px;
    background-color: white;
}

.con_div_text {
    height: 100%;
    width: 32.2%;
    margin-right: 5px;
}

.con_div_text01 {
    width: 100%;
    height: 100%;
    border: 2px solid white;
    border-radius: 10px;
    color: white;
}

.text01_img {
    width: 60px;
    height: 60px;
    margin-left: 5%;
    margin-top: 40px;
}

.text01_div {
    margin-top: 25px;
    margin-left: 5%;
    text-align: center;
}

.text01_div p {
    line-height: 35px;
}

.text01_div p:nth-child(1) {
    font-size: 20px;
}

.text01_div p:nth-child(2) {
    font-size: 40px;
    font-weight: 600;
}

.div_any {
    width: 98%;
    margin-left: 1%;
    margin-bottom: 25px;
    height: 100%;
}

.div_any01 {
    width: 30%;
    height: 99%;
}

.div_any02 {
    width: 69%;
    height: 99%;
    margin-left: 10px;
    overflow: hidden;
}

.div_any_child {
    width: 100%;
    height: 27%;
    /* min-height: 300px; */
    box-sizing: border-box;
    position: relative;
    margin-top: 25px;
    background-color: white;
}

.p_chart {
    height: 300px;
    /* padding: 5px 5px;
    margin-top: 10px; */
}

.div_any_title {
    background-color: #0b64a3;
    border-radius: 5px;
    position: absolute;
    height: 35px;
    width: 60%;
    top: -15px;
    color: #ffffff;
    font-weight: bold;
    font-size: 16px;
    left: 20%;
    line-height: 35px;
    text-align: center;
}

.div_any_title img {
    width: 18px;
    height: 18px;
    position: relative;
    top: 2px;
    margin-right: 5px;
}

.hide {
    display: none !important;
}

.xc_layer {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: #666666;
    opacity: 0.5;
    z-index: 1000;
}

.popBox {
    position: fixed;
    left: 40%;
    top: 10%;
    background-color: white;
    z-index: 1001;
    width: 50%;
    max-height: 50%;
    margin-left: -15%;
    border-radius: 5px;
    font-weight: bold;
    height: 100%;
}

.popBox .ttBox {
    height: 30px;
    line-height: 30px;
    padding: 14px 30px;
    border-bottom: solid 1px #eef0f1;
    text-align: center;
    -webkit-box-sizing: content-box;
    -moz-box-sizing: content-box;
    box-sizing: content-box;
}

.popBox .ttBox .tt {
    font-size: 20px;
    display: inline-block;
    height: 30px;
}

.popBox .txtBox {
    height: calc(100% - 80px);
    overflow: auto;
    margin: 10px;
}