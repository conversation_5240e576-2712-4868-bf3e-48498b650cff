---
description: 
globs: 
alwaysApply: true
---
# Vben LIMS Pharma QC 项目指南

## 项目架构概览

这是一个基于 Vben Admin 的 LIMS（实验室信息管理系统）项目，采用 Monorepo 架构：

### 核心应用
- **backend-mock**: 后端模拟服务，基于 Nitro，提供 API 接口模拟
- **web-antd**: 前端主应用，基于 Vue 3 + Ant Design Vue + TypeScript
- **playground**: 开发测试环境

### 包结构
- **@core**: 核心功能包（base、composables、preferences、ui-kit）
- **packages**: 共享包（constants、effects、icons、locales、stores、styles、types、utils）
- **internal**: 内部工具包（lint-configs、node-utils、tailwind-config、vite-config）

## 开发规范

### 1. 代码风格
- 遵循 [开发规范.md](mdc:开发规范.md) 中的详细规范
- 使用 TypeScript 进行开发，确保类型安全
- 采用 ESLint + Prettier 进行代码格式化
- 统一使用中文注释和文档

### 2. 目录结构规范

#### 前端应用结构 ([apps/web-antd/src](mdc:apps/web-antd/src))
```
src/
├── adapter/          # 适配器层
├── api/             # API 接口定义
├── components/      # 公共组件
├── hooks/           # 组合式函数
├── layouts/         # 布局组件
├── locales/         # 国际化文件
├── router/          # 路由配置
├── store/           # 状态管理
├── types/           # 类型定义
├── utils/           # 工具函数
└── views/           # 页面组件
```

#### 组件开发规范
- 组件文件使用 PascalCase 命名
- 每个复杂组件应有对应的类型定义文件
- 使用 Vue 3 Composition API + `<script setup>` 语法
- 组件结构按顺序：imports → props/emits → 状态 → 计算属性 → 方法 → 生命周期

### 3. 表单开发规范

使用 Vben Form 组件：
```typescript
const schema: VbenFormSchema[] = [
  {
    component: 'Input',
    fieldName: 'name',
    label: $t('common.name'),
    rules: z.string().min(1, '请输入名称'),
  }
];
```

### 4. 表格开发规范

使用 VxeGrid 组件：
```typescript
const [Grid, gridApi] = useVbenVxeGrid({
  gridOptions: {
    columns: useColumns(),
    proxyConfig: {
      ajax: {
        query: async (params) => await getList(params),
      },
    },
  },
});
```

### 5. API 开发规范

#### 后端模拟 ([apps/backend-mock/api](mdc:apps/backend-mock/api))
- 使用 Nitro 框架
- API 路由按业务模块组织
- 统一的响应格式和错误处理
- 使用 [apps/backend-mock/utils/mock-data.ts](mdc:apps/backend-mock/utils/mock-data.ts) 生成模拟数据

#### 前端 API 调用
```typescript
// 定义接口类型
export interface UserApi {
  getList: (params: QueryParams) => Promise<PageResult<User>>;
  create: (data: CreateUserDto) => Promise<void>;
}

// 使用统一的请求封装
import { requestClient } from '#/api/request';
```

### 6. 状态管理规范

使用 Pinia：
- Store 文件放在 [packages/stores/src/modules](mdc:packages/stores/src/modules) 目录
- 按业务模块划分 Store
- 使用 TypeScript 定义 Store 类型

### 7. 国际化规范

#### 翻译键命名
- 模块级：`module.xxx`
- 页面级：`module.page.xxx`
- 组件级：`module.component.xxx`
- 通用文本：`common.xxx`

#### 使用方式
```typescript
// 在组件中
import { $t } from '#/locales';
const title = $t('system.user.title');
```

### 8. 样式开发规范

- 优先使用 Tailwind CSS 工具类
- 组件样式使用 `scoped`
- 主题配置在 [packages/styles](mdc:packages/styles) 目录
- 全局样式变量使用 CSS 变量

### 9. 路由配置规范

路由文件在 [apps/web-antd/src/router](mdc:apps/web-antd/src/router)：
- 使用路由懒加载
- 配置路由权限 `access`
- 支持多级嵌套路由
- 路由元信息包含标题、图标等

### 10. 权限控制规范

#### 权限指令
```vue
<VbenButton v-access:code="['System.User.Create']">
  创建用户
</VbenButton>
```

#### 权限码命名
格式：`模块名.操作名`，如：`System.User.Create`

## 特殊组件说明

### Logic Flow 工作流组件
位于 [apps/web-antd/src/components/logic-flow](mdc:apps/web-antd/src/components/logic-flow)：
- 基于 LogicFlow 的可视化工作流设计器
- 支持多种节点类型（开始、结束、用户任务、条件判断等）
- 节点配置在 [apps/web-antd/src/components/logic-flow/config/nodes.ts](mdc:apps/web-antd/src/components/logic-flow/config/nodes.ts)
- 工作流类型定义在 [apps/web-antd/src/components/logic-flow/types/workflow.ts](mdc:apps/web-antd/src/components/logic-flow/types/workflow.ts)

## 开发工具和脚本

### 构建和开发
- 使用 Turbo 进行 Monorepo 管理
- 开发服务器：`pnpm dev`
- 构建：`pnpm build`
- 类型检查：`pnpm typecheck`

### 代码质量
- ESLint 配置：[internal/lint-configs/eslint-config](mdc:internal/lint-configs/eslint-config)
- Prettier 配置：[internal/lint-configs/prettier-config](mdc:internal/lint-configs/prettier-config)
- 提交规范：[internal/lint-configs/commitlint-config](mdc:internal/lint-configs/commitlint-config)

### 测试
- E2E 测试：使用 Playwright，配置在 [playground/__tests__/e2e](mdc:playground/__tests__/e2e)
- 单元测试：使用 Vitest

## 常见模式和最佳实践

1. **组件通信**：优先使用 props/emits，复杂状态使用 Pinia
2. **错误处理**：统一使用 message 组件显示错误信息
3. **性能优化**：合理使用 `keep-alive`、路由懒加载、组件按需引入
4. **类型安全**：充分利用 TypeScript 类型系统，避免使用 `any`
5. **代码复用**：公共逻辑抽取为 composables，公共组件放在 components 目录

## 部署和发布

- Docker 部署配置：[scripts/deploy](mdc:scripts/deploy)
- 环境配置：支持多环境部署
- 自动化构建：基于 GitHub Actions 或其他 CI/CD 工具
